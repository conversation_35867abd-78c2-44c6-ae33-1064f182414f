/*打包session的json结构*/

#ifndef DPI_ARKIME_SESSION_H
#define DPI_ARKIME_SESSION_H

#include <stdint.h>
#include "dpi_arkime_pcap.h"

#ifdef __cplusplus
extern "C" {
#endif

// 前向声明
struct flow_info;
typedef struct ProtoRecord           precord_t;
/**
 * 发送会话数据到ES - 完全基于record
 * @param record 协议记录指针（包含所有必要信息）
 * @return 0成功，-1失败
 */
int dpi_arkime_send_session_to_es(struct arkime_file_pos_node *node,precord_t *record);

#ifdef __cplusplus
}
#endif

#endif /* DPI_ARKIME_SESSION_H */
