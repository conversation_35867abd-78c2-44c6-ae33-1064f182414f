/*
 * Arkime字段注册模块
 * 遍历record注册过的字段，生成ES字段注册消息
 * 字段格式：
 * - type: 全部为termfield
 * - _id: layer.field
 * - friendlyName: field_name
 * - group: layer名
 * - help: 不填充
 * - dbField2: 与_id一样
 */

#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <unistd.h>

#include "../../include/cJSON.h"
#include "../dpi_detect.h"
#include "../dpi_log.h"
#include "../dpi_lua_adapt.h"
#include "../dpi_pschema.h"
#include "dpi_arkime_es.h"
#include "dpi_arkime_field.h"

// 外部全局配置变量
extern struct global_config g_config;

// 字段注册状态 - 使用原子操作替代互斥锁
static volatile int g_fields_registered = 0;

// 线程本地字段注册标志
static __thread int tls_fields_registered = 0;

// 创建字段定义JSON
static char *create_arkime_field_json(const char *layer_name, const char *field_name) {
  cJSON *field_def = cJSON_CreateObject();
  if (!field_def) {
    return NULL;
  }

  // 设置字段属性
  cJSON_AddStringToObject(field_def, "friendlyName", field_name);
  cJSON_AddStringToObject(field_def, "group", layer_name);
  cJSON_AddStringToObject(field_def, "help", "");           // 不填充help
  cJSON_AddStringToObject(field_def, "type", "termfield");  // 全部为termfield

  // 构造dbField2 (layer.field格式)
  char dbfield2[256];
  snprintf(dbfield2, sizeof(dbfield2), "%s.%s", layer_name, field_name);
  cJSON_AddStringToObject(field_def, "dbField2", dbfield2);

  char *json_string = cJSON_PrintUnformatted(field_def);
  cJSON_Delete(field_def);

  return json_string;
}

// 注册单个协议的字段 (使用bulk API发送)
static int register_proto_fields(pschema_t *schema) {
  if (!schema) {
    return -1;
  }

  const char *proto_name = pschema_get_proto_name(schema);
  if (!proto_name) {
    return -1;
  }

  // 跳过一些特殊的协议
  if (strcmp(proto_name, "common") == 0 || strcmp(proto_name, "327_common") == 0 || strcmp(proto_name, "link") == 0) {
    return 0;
  }

  DPI_LOG(DPI_LOG_DEBUG, "Registering fields for protocol: %s using bulk API", proto_name);

  // 构建bulk数据
  char *bulk_data = malloc(64 * 1024);  // 64KB缓冲区
  if (!bulk_data) {
    DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for bulk field data");
    return -1;
  }

  int bulk_len = 0;
  int field_count = 0;

  // 遍历协议的所有字段，构建bulk请求
  for (pfield_desc_t *fdesc = pschema_fdesc_get_first(schema); fdesc != NULL;
       fdesc                = pschema_fdesc_get_next(schema, fdesc)) {
    const char *field_name = pfdesc_get_name(fdesc);
    if (!field_name) {
      continue;
    }

    // 构造字段ID (layer.field格式)
    char field_id[256];
    snprintf(field_id, sizeof(field_id), "%s.%s", proto_name, field_name);

    // 创建字段定义JSON
    char *field_json = create_arkime_field_json(proto_name, field_name);
    if (!field_json) {
      DPI_LOG(DPI_LOG_WARNING, "Failed to create field JSON for %s.%s", proto_name, field_name);
      continue;
    }

    // 添加bulk header
    int header_len = snprintf(bulk_data + bulk_len, 64 * 1024 - bulk_len,
                              "{\"index\":{\"_index\":\"arkime_fields\",\"_id\":\"%s\"}}\n", field_id);
    if (header_len < 0 || bulk_len + header_len >= 64 * 1024 - 1024) {
      DPI_LOG(DPI_LOG_WARNING, "Bulk buffer full, sending partial batch");
      free(field_json);
      break;
    }
    bulk_len += header_len;

    // 添加字段定义JSON
    int json_len = snprintf(bulk_data + bulk_len, 64 * 1024 - bulk_len, "%s\n", field_json);
    if (json_len < 0 || bulk_len + json_len >= 64 * 1024) {
      DPI_LOG(DPI_LOG_WARNING, "Bulk buffer full, sending partial batch");
      free(field_json);
      break;
    }
    bulk_len += json_len;

    free(field_json);
    field_count++;
  }

  // 发送bulk请求
  int result = 0;
  if (bulk_len > 0) {
    result = dpi_arkime_es_send_bulk_data(bulk_data);
    if (result == 0) {
      DPI_LOG(DPI_LOG_INFO, "Successfully registered %d fields for protocol: %s using bulk API", field_count, proto_name);
    } else {
      DPI_LOG(DPI_LOG_WARNING, "Failed to register fields for protocol: %s using bulk API", proto_name);
    }
  }

  free(bulk_data);
  return (result == 0) ? field_count : 0;
}

// 注册lua_adapt后的协议字段
int dpi_arkime_register_lua_adapt_fields(void) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping lua adapt fields registration");
    return 0;
  }

  extern padapt_controller_t *g_padapt_controller;

  if (!g_padapt_controller) {
    DPI_LOG(DPI_LOG_WARNING, "Lua adapt controller not initialized");
    return -1;
  }

  // 获取lua_adapt后的schema数据库
  pschema_db_t *adapted_db = padapt_controller_get_adapted_schema_db(g_padapt_controller);
  if (!adapted_db) {
    DPI_LOG(DPI_LOG_WARNING, "No adapted schema database available");
    return -1;
  }

  DPI_LOG(DPI_LOG_INFO, "Starting lua_adapt field registration to Elasticsearch");

  int total_fields = 0;
  int proto_count  = 0;

  // 遍历lua_adapt后的所有协议
  for (pschema_t *schema = pschema_get_first(adapted_db); schema != NULL;
       schema            = pschema_get_next(adapted_db, schema)) {
    int field_count = register_proto_fields(schema);
    if (field_count > 0) {
      total_fields += field_count;
      proto_count++;
    }
  }

  DPI_LOG(DPI_LOG_INFO, "Lua_adapt field registration completed: %d fields from %d protocols", total_fields,
          proto_count);

  return total_fields;
}

// 注册所有已注册协议的字段 - 使用原子操作和线程本地标志
int dpi_arkime_register_all_fields(void) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping all fields registration");
    return 0;
  }

  // 检查线程本地标志，避免同一线程重复注册
  if (tls_fields_registered) {
    DPI_LOG(DPI_LOG_DEBUG, "Fields already registered by this thread, skipping");
    return 0;
  }

  // 检查全局标志，使用原子操作避免竞争
  if (__sync_val_compare_and_swap(&g_fields_registered, 0, 1) != 0) {
    // 已经被其他线程注册过
    tls_fields_registered = 1;  // 设置线程本地标志
    DPI_LOG(DPI_LOG_INFO, "Fields already registered by another thread, skipping");
    return 0;
  }

  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    // 重置全局标志，允许下次尝试
    __sync_bool_compare_and_swap(&g_fields_registered, 1, 0);
    DPI_LOG(DPI_LOG_INFO, "ES disabled, skipping field registration");
    return 0;
  }

  DPI_LOG(DPI_LOG_INFO, "Starting field registration to Elasticsearch");

  int total_fields = 0;
  int proto_count  = 0;

  // 优先使用lua_adapt后的字段
  int lua_adapt_fields = dpi_arkime_register_lua_adapt_fields();
  if (lua_adapt_fields > 0) {
    total_fields += lua_adapt_fields;
    DPI_LOG(DPI_LOG_INFO, "Registered %d lua_adapt fields", lua_adapt_fields);
  } else {
    // 如果lua_adapt失败，回退到原始schema
    DPI_LOG(DPI_LOG_INFO, "Lua_adapt registration failed, using original schemas");

    // 遍历所有已注册的协议
    for (pschema_t *schema = dpi_pschema_get_first(); schema != NULL; schema = dpi_pschema_get_next(schema)) {
      int field_count = register_proto_fields(schema);
      if (field_count > 0) {
        total_fields += field_count;
        proto_count++;
      }
    }
  }

  // 设置线程本地标志
  tls_fields_registered = 1;

  DPI_LOG(DPI_LOG_INFO, "Field registration completed: %d total fields", total_fields);

  return total_fields;
}

// 注册单个协议的字段（用于动态注册）
int dpi_arkime_register_proto_fields(const char *proto_name) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping proto fields registration for %s",
            proto_name ? proto_name : "NULL");
    return 0;
  }

  if (!proto_name) {
    DPI_LOG(DPI_LOG_ERROR, "Invalid protocol name");
    return -1;
  }

  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    DPI_LOG(DPI_LOG_DEBUG, "ES disabled, skipping field registration for %s", proto_name);
    return 0;
  }

  // 获取协议schema
  pschema_t *schema = dpi_pschema_get_proto(proto_name);
  if (!schema) {
    DPI_LOG(DPI_LOG_WARNING, "Protocol schema not found: %s", proto_name);
    return -1;
  }

  DPI_LOG(DPI_LOG_DEBUG, "Registering fields for protocol: %s", proto_name);

  int field_count = register_proto_fields(schema);

  if (field_count > 0) {
    DPI_LOG(DPI_LOG_INFO, "Registered %d fields for protocol: %s", field_count, proto_name);
  }

  return field_count;
}

// 重置字段注册状态（用于测试）- 使用原子操作
void dpi_arkime_reset_field_registration(void) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    DPI_LOG(DPI_LOG_DEBUG, "Arkime ES module disabled, skipping field registration reset");
    return;
  }

  __sync_bool_compare_and_swap(&g_fields_registered, 1, 0);
  tls_fields_registered = 0;  // 重置线程本地标志
  DPI_LOG(DPI_LOG_DEBUG, "Field registration status reset");
}

// 检查字段是否已注册 - 使用原子读取
int dpi_arkime_is_fields_registered(void) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    return 1;  // ES未启用时返回已注册状态，避免重复检查
  }

  return __sync_fetch_and_add(&g_fields_registered, 0);  // 原子读取
}

// 获取字段注册统计信息
int dpi_arkime_get_field_stats(int *proto_count, int *field_count) {
  if (!proto_count || !field_count) {
    return -1;
  }

  *proto_count = 0;
  *field_count = 0;

  // 遍历所有协议统计字段数量
  for (pschema_t *schema = dpi_pschema_get_first(); schema != NULL; schema = dpi_pschema_get_next(schema)) {
    const char *proto_name = pschema_get_proto_name(schema);
    if (!proto_name) {
      continue;
    }

    // 跳过特殊协议
    if (strcmp(proto_name, "common") == 0 || strcmp(proto_name, "327_common") == 0 || strcmp(proto_name, "link") == 0) {
      continue;
    }

    int fields_in_proto = 0;
    for (pfield_desc_t *fdesc = pschema_fdesc_get_first(schema); fdesc != NULL;
         fdesc                = pschema_fdesc_get_next(schema, fdesc)) {
      fields_in_proto++;
    }

    if (fields_in_proto > 0) {
      (*proto_count)++;
      (*field_count) += fields_in_proto;
    }
  }

  return 0;
}
