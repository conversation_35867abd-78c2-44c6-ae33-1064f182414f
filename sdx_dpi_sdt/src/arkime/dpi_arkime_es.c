/*
 * Arkime Elasticsearch发送模块
 * 使用libcurl发送打包好的json结构到Elasticsearch
 * 支持三种发送接口：
 * 1. arkime_sessions3-{YYMMDD} - 会话数据
 * 2. arkime_files_v30/_doc/localhost-{fileid} - 文件信息
 * 3. arkime_fields - 字段定义
 */

#include <curl/curl.h>
#include <pthread.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <time.h>
#include <unistd.h>

#include "../dpi_detect.h"
#include "../dpi_log.h"
#include "cJSON.h"
#include "dpi_arkime_es.h"
#include "iniparser/iniparser.h"

// 外部全局配置变量
extern struct global_config g_config;

// HTTP响应结构
typedef struct {
  char  *data;
  size_t size;
} http_response_t;

// 线程本地CURL句柄 - 每个线程独立的curl句柄，避免锁竞争
static __thread CURL *tls_curl_handle = NULL;

// HTTP响应回调函数
static size_t write_response_callback(void *contents, size_t size, size_t nmemb, http_response_t *response) {
  size_t total_size = size * nmemb;
  char  *ptr        = realloc(response->data, response->size + total_size + 1);
  if (!ptr) {
    DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for HTTP response");
    return 0;
  }

  response->data = ptr;
  memcpy(&(response->data[response->size]), contents, total_size);
  response->size += total_size;
  response->data[response->size] = '\0';

  return total_size;
}

// 全局初始化ES模块（只初始化libcurl全局状态）
int dpi_arkime_es_init(void) {
  // 初始化libcurl全局状态
  if (curl_global_init(CURL_GLOBAL_DEFAULT) != CURLE_OK) {
    DPI_LOG(DPI_LOG_ERROR, "Failed to initialize libcurl");
    return -1;
  }

  DPI_LOG(DPI_LOG_INFO, "Arkime ES module initialized successfully");
  return 0;
}

// 线程本地初始化CURL句柄
static int init_thread_curl_handle(void) {
  if (tls_curl_handle) {
    return 0;  // 已经初始化过
  }

  // 创建线程本地CURL句柄
  tls_curl_handle = curl_easy_init();
  if (!tls_curl_handle) {
    DPI_LOG(DPI_LOG_ERROR, "Failed to create thread-local CURL handle");
    return -1;
  }

  // 设置基本选项
  curl_easy_setopt(tls_curl_handle, CURLOPT_TIMEOUT, g_config.es_config.timeout_seconds);
  curl_easy_setopt(tls_curl_handle, CURLOPT_CONNECTTIMEOUT, 10L);
  curl_easy_setopt(tls_curl_handle, CURLOPT_WRITEFUNCTION, write_response_callback);
  curl_easy_setopt(tls_curl_handle, CURLOPT_USERAGENT, "sdx_dpi_sdt/1.0");
  curl_easy_setopt(tls_curl_handle, CURLOPT_FOLLOWLOCATION, 1L);
  curl_easy_setopt(tls_curl_handle, CURLOPT_MAXREDIRS, 3L);

  // 如果配置了用户名密码，设置认证
  if (strlen(g_config.es_config.es_username) > 0) {
    char userpwd[128];
    snprintf(userpwd, sizeof(userpwd), "%s:%s", g_config.es_config.es_username, g_config.es_config.es_password);
    curl_easy_setopt(tls_curl_handle, CURLOPT_USERPWD, userpwd);
  }

  DPI_LOG(DPI_LOG_DEBUG, "Thread-local CURL handle initialized");
  return 0;
}

// 清理线程本地CURL句柄
void dpi_arkime_es_cleanup_thread(void) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    return;
  }

  if (tls_curl_handle) {
    curl_easy_cleanup(tls_curl_handle);
    tls_curl_handle = NULL;
    DPI_LOG(DPI_LOG_DEBUG, "Thread-local CURL handle cleaned up");
  }
}

// 清理ES模块（全局清理）
void dpi_arkime_es_cleanup(void) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    return;
  }

  curl_global_cleanup();
  DPI_LOG(DPI_LOG_INFO, "Arkime ES module cleaned up");
}

// 设置ES配置（无需锁保护，配置通常在初始化时设置）
void dpi_arkime_es_set_config(const char *host, int port, const char *username, const char *password,
                              const char *node_name, int timeout, int enabled) {
  if (host) {
    strncpy(g_config.es_config.es_host, host, sizeof(g_config.es_config.es_host) - 1);
    g_config.es_config.es_host[sizeof(g_config.es_config.es_host) - 1] = '\0';
  }

  if (port > 0) {
    g_config.es_config.es_port = port;
  }

  if (username) {
    strncpy(g_config.es_config.es_username, username, sizeof(g_config.es_config.es_username) - 1);
    g_config.es_config.es_username[sizeof(g_config.es_config.es_username) - 1] = '\0';
  }

  if (password) {
    strncpy(g_config.es_config.es_password, password, sizeof(g_config.es_config.es_password) - 1);
    g_config.es_config.es_password[sizeof(g_config.es_config.es_password) - 1] = '\0';
  }

  if (node_name) {
    strncpy(g_config.es_config.node_name, node_name, sizeof(g_config.es_config.node_name) - 1);
    g_config.es_config.node_name[sizeof(g_config.es_config.node_name) - 1] = '\0';
  }

  if (timeout > 0) {
    g_config.es_config.timeout_seconds = timeout;
  }

  g_config.es_config.enabled = enabled;

  DPI_LOG(DPI_LOG_INFO, "ES config updated: host=%s:%d, node=%s, enabled=%d", g_config.es_config.es_host,
          g_config.es_config.es_port, g_config.es_config.node_name, g_config.es_config.enabled);
}

// 通用HTTP POST发送函数
static int send_http_post(const char *url, const char *json_data, const char *content_type) {
  if (!g_config.es_config.enabled) {
    return 0;  // 如果未启用ES，直接返回成功
  }

  // 确保线程本地CURL句柄已初始化
  if (init_thread_curl_handle() != 0) {
    DPI_LOG(DPI_LOG_ERROR, "Failed to initialize thread-local CURL handle");
    return -1;
  }

  http_response_t response = {0};
  CURLcode        res;
  long            response_code = 0;

  // 设置URL
  curl_easy_setopt(tls_curl_handle, CURLOPT_URL, url);

  // 设置POST数据
  curl_easy_setopt(tls_curl_handle, CURLOPT_POSTFIELDS, json_data);
  curl_easy_setopt(tls_curl_handle, CURLOPT_POSTFIELDSIZE, strlen(json_data));

  // 设置HTTP头
  struct curl_slist *headers = NULL;
  char               content_type_header[256];
  snprintf(content_type_header, sizeof(content_type_header), "Content-Type: %s", content_type);
  headers = curl_slist_append(headers, content_type_header);
  headers = curl_slist_append(headers, "Accept: application/json");
  curl_easy_setopt(tls_curl_handle, CURLOPT_HTTPHEADER, headers);

  // 如果配置了认证，设置认证
  if (strlen(g_config.es_config.es_username) > 0) {
    char userpwd[128];
    snprintf(userpwd, sizeof(userpwd), "%s:%s", g_config.es_config.es_username, g_config.es_config.es_password);
    curl_easy_setopt(tls_curl_handle, CURLOPT_USERPWD, userpwd);
  }

  // 设置响应回调
  curl_easy_setopt(tls_curl_handle, CURLOPT_WRITEDATA, &response);

  // 执行请求
  res = curl_easy_perform(tls_curl_handle);

  // 获取响应码
  curl_easy_getinfo(tls_curl_handle, CURLINFO_RESPONSE_CODE, &response_code);

  // 清理
  curl_slist_free_all(headers);

  // 检查结果
  if (res != CURLE_OK) {
    DPI_LOG(DPI_LOG_ERROR, "HTTP POST failed: %s", curl_easy_strerror(res));
    if (response.data) free(response.data);
    return -1;
  }

  if (response_code < 200 || response_code >= 300) {
    DPI_LOG(DPI_LOG_WARNING, "HTTP POST returned code %ld for URL: %s", response_code, url);
    DPI_LOG(DPI_LOG_DEBUG, "Response: %s", response.data ? response.data : "");
    if (response.data) free(response.data);
    return -1;
  }

  DPI_LOG(DPI_LOG_DEBUG, "HTTP POST successful: %ld for URL: %s", response_code, url);
  if (response.data) free(response.data);
  return 0;
}

// 获取当前日期字符串 (YYMMDD格式)
static void get_date_string(char *date_str, size_t size) {
  time_t     now     = time(NULL);
  struct tm *tm_info = localtime(&now);
  strftime(date_str, size, "%y%m%d", tm_info);
}

// 1. 发送会话数据到 arkime_sessions3-{YYMMDD} - 使用bulk格式
int dpi_arkime_es_send_session(const char *session_json) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    return 0;  // ES未启用时直接返回成功
  }

  if (!session_json || strlen(session_json) == 0) {
    DPI_LOG(DPI_LOG_WARNING, "Empty session JSON data");
    return -1;
  }

  char date_str[16];
  get_date_string(date_str, sizeof(date_str));

  // 构造bulk格式数据
  char *bulk_data = malloc(strlen(session_json) + 256);
  if (!bulk_data) {
    DPI_LOG(DPI_LOG_ERROR, "Failed to allocate memory for bulk data");
    return -1;
  }

  // 构造bulk header
  int bulk_len = snprintf(bulk_data, strlen(session_json) + 256,
                          "{\"index\":{\"_index\":\"arkime_sessions3-%s\"}}\n%s\n", date_str, session_json);

  // 使用bulk API发送
  int result = dpi_arkime_es_send_bulk_data(bulk_data);

  free(bulk_data);
  return result;
}

// 2. 发送文件信息到 arkime_files_v30/_doc/{nodename}-{fileid}
int dpi_arkime_es_send_file_info(uint64_t file_id, const char *file_json) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    return 0;  // ES未启用时直接返回成功
  }

  if (!file_json || strlen(file_json) == 0) {
    DPI_LOG(DPI_LOG_WARNING, "Empty file JSON data");
    return -1;
  }

  char url[512];
  snprintf(url, sizeof(url), "http://%s:%d/arkime_files_v30/_doc/%s-%lu", g_config.es_config.es_host,
           g_config.es_config.es_port, g_config.es_config.node_name, file_id);

  DPI_LOG(DPI_LOG_DEBUG, "Sending file info to: %s", url);
  return send_http_post(url, file_json, "application/json");
}

// 3. 发送字段定义到 arkime_fields (单个字段) - 纯发送功能
int dpi_arkime_es_send_field_definition(const char *field_id, const char *field_json) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    return 0;  // ES未启用时直接返回成功
  }

  if (!field_id || !field_json || strlen(field_json) == 0) {
    DPI_LOG(DPI_LOG_WARNING, "Invalid field definition data");
    return -1;
  }

  char url[512];
  snprintf(url, sizeof(url), "http://%s:%d/arkime_fields/_doc/%s", g_config.es_config.es_host,
           g_config.es_config.es_port, field_id);

  DPI_LOG(DPI_LOG_DEBUG, "Sending field definition to: %s", url);
  return send_http_post(url, field_json, "application/json");
}

// 批量发送预构造的bulk数据到ES - 纯发送功能
int dpi_arkime_es_send_bulk_data(const char *bulk_data) {
  // 检查ES是否启用
  if (!g_config.es_config.enabled) {
    return 0;  // ES未启用时直接返回成功
  }

  if (!bulk_data || strlen(bulk_data) == 0) {
    DPI_LOG(DPI_LOG_WARNING, "Invalid bulk data");
    return -1;
  }

  char url[512];
  snprintf(url, sizeof(url), "http://%s:%d/_bulk", g_config.es_config.es_host, g_config.es_config.es_port);

  DPI_LOG(DPI_LOG_DEBUG, "Sending bulk data to: %s", url);
  return send_http_post(url, bulk_data, "application/x-ndjson");
}

// 从配置文件初始化ES模块配置
int dpi_arkime_es_init_config(void *ini) {
  if (!ini) {
    DPI_LOG(DPI_LOG_WARNING, "No configuration provided for ES module");
    return 0;
  }

  // 这里需要根据实际的配置文件解析库来实现
  // 假设使用iniparser库
  dictionary *config = (dictionary *)ini;

  // 读取ES配置
  const char *es_host     = iniparser_getstring(config, "arkime:ES_HOST", "localhost");
  int         es_port     = iniparser_getint(config, "arkime:ES_PORT", 9200);
  const char *es_username = iniparser_getstring(config, "arkime:ES_USERNAME", "");
  const char *es_password = iniparser_getstring(config, "arkime:ES_PASSWORD", "");
  const char *node_name   = iniparser_getstring(config, "arkime:ARKIME_NODE_NAME", "localhost");
  int         timeout     = iniparser_getint(config, "arkime:ES_TIMEOUT", 30);
  int         enabled     = iniparser_getint(config, "arkime:ES_ENABLE", 0);

  // 设置配置
  dpi_arkime_es_set_config(es_host, es_port, es_username, es_password, node_name, timeout, enabled);

  // 如果启用了ES，初始化模块
  if (enabled) {
    if (dpi_arkime_es_init() != 0) {
      DPI_LOG(DPI_LOG_ERROR, "Failed to initialize ES module");
      return -1;
    }
    DPI_LOG(DPI_LOG_INFO, "ES module initialized with config: %s:%d", es_host, es_port);
  } else {
    DPI_LOG(DPI_LOG_INFO, "ES module disabled in configuration");
  }

  return 0;
}
