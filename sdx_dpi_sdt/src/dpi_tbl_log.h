/****************************************************************************************
 * 文 件 名 : dpi_tbl_log.h
 * 项目名称 :
 * 模 块 名 :
 * 功    能 :
 * 操作系统 : LINUX
 * 修改记录 : 无
 * 版    本 : Rev 0.1.0
 *- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
设计: wangy              2018/07/06
编码: wangy            2018/07/06
修改:
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -
* 公司介绍及版权说明
*
*           (C)Copyright 2018 YView    Corporation All Rights Reserved.
*- - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - - -

*****************************************************************************************/

#ifndef _DPI_TBL_LOG_H_
#define _DPI_TBL_LOG_H_

#include "arkime/dpi_arkime_pcap.h"
#include "dpi_detect.h"
#include "dpi_trailer.h"
#include "sdtapp_interface.h"
#include "dpi_sdt_match.h"
#include "dpi_share_header.h"
#include "dpi_recoder.h"
#include "yaProtoRecord/precord_index.h"

#define TBL_LOG_RULE_INFO         256
#define TBL_LOG_MAX_LEN           (1024 * 20)
#define TBL_LOG_CONTENT_256K_NUM  4096

#define KV53_KV99_LEN ((99 - 53 + 1) * (2))

#define TBL_MAX_BURST             512
#define TBL_FILE_TIMEOUT          5
#define MAX_FIELD_LEN             64
#define PROTO_MAX_FIELDS_NUM      300
#define IP_INFO_BUFF_SIZE         64

#define SDT_STACK_STREAM_SIZE     8192
#define SDT_STACK_STREAM_PKTS     8

extern const char *empty_data;

extern int tbl_out_thfunc_signal;

#define TBL_LOG_MATCH_MAX    128
struct tbl_log {
    rte_atomic16_t  ref_cnt;
    int      proto_id;
    int      thread_id;
    int      log_type;
    int      log_len;
    int      content_len;
    uint8_t  *content_ptr;

    struct flow_info *flow;
    struct arkime_file_pos_node *node;//flow中的此结构是多线程可见的，所在在初始化tbl_log时，进行一次深拷贝
    sdt_out_status  *out_elem;
    // 多模存储结果
    uint8_t         match_res_cnt;
    bool            add_task[TBL_LOG_MATCH_MAX];  // record 重是否添加任务信息
    sdt_out_status *match_info[TBL_LOG_MATCH_MAX];

    char log_rule[TBL_LOG_RULE_INFO];
    precord_t *record;

    struct mac_packet_header   mac_hdr;  // 命中转发时的mac头， 暂时放在这里传给匹配线程，后面优化
    //缓存解析报文数据，用于匹配输出(SPO_out_pkt)当前报文
#ifdef DPI_FUTURE_MBUF
    struct pkt_info  pkt;
#else
    uint8_t          pkt_data[2000]; /* 报文数据存储，从以太层开始，包括trailer数据 */
    uint16_t         pkt_data_len;   /* 报文总长度，包括trailer数据长度 */
#endif

    uint16_t         match_data_len; /* tcp重组回调解析函数中的待解析的报文长度. 用于匹配信息统计,超时预警.
                                        传递路径: flow -> tbl -> record */
};


/************************************   test for field write **************************************/

typedef enum _data_type{
    EM_F_TYPE_EMPTY,
    EM_F_TYPE_UINT8  = FT_UINT8,
    EM_F_TYPE_UINT16 = FT_UINT16,
    EM_F_TYPE_UINT32 = FT_UINT32,
    EM_F_TYPE_UINT64 = FT_UINT64,
    EM_F_TYPE_STRING = FT_STRING,
    EM_F_TYPE_HEX    = FT_MAC+1,
    EM_F_TYPE_TIMESTAMP,
    EM_F_TYPE_NULL,
    EM_F_TYPE_MAX
}data_type;

typedef int (*field_callback)(char *, int *, int , const char, unsigned int );

typedef struct _dpi_file_table_type_t{
    uint32_t       code;
    const char     file_type[32];
}dpi_file_table_type;

typedef struct _dpi_ip_info
{
    char city[IP_INFO_BUFF_SIZE];
    char state[IP_INFO_BUFF_SIZE];
    char country[IP_INFO_BUFF_SIZE];
    char area[IP_INFO_BUFF_SIZE];
    char isp[IP_INFO_BUFF_SIZE];

    char asn[IP_INFO_BUFF_SIZE];
    char latitude[IP_INFO_BUFF_SIZE];
    char longitude[IP_INFO_BUFF_SIZE];
} IPINFO;

enum _dpi_sdt_rule_en{
    EM_SDT_RULE_ID,
    EM_SDT_RULE_HASH_CODE,
    EM_SDT_RULE_GID,
    EM_SDT_RULE_UUID,
    EM_SDT_RULE_SCORE,
    EM_SDT_RULE_MSG,
    EM_SDT_RULE_CLASSTYPE,
    EM_SDT_RULE_REFERENCE,
    EM_SDT_RULE_ETAGS,
    EM_SDT_RULE_TTAGS,
    EM_SDT_RULE_MAX,
};



typedef enum _dpi_ip_info_em{
    EM_IP_INFO_TIME,
    EM_IP_INFO_ETIME,
    EM_IP_INFO_TIMELEN,
    EM_IP_INFO_SRCMAC,
    EM_IP_INFO_DSTMAC,
    EM_IP_INFO_PORTNUM,
    EM_IP_INFO_SRCADDR,
    EM_IP_INFO_DSTADDR,
    EM_IP_INFO_INNSRCADDR,
    EM_IP_INFO_INNDSTADDR,
    EM_IP_INFO_ADDRTYPE,
    EM_IP_INFO_INNADDRTYPE,
    EM_IP_INFO_FIRTTLBYCLI,
    EM_IP_INFO_FIRTTLBYVSR,
    EM_IP_INFO_SRCPORT,
    EM_IP_INFO_DSTPORT,
    EM_IP_INFO_APPPROT,
    EM_IP_INFO_STRDIREC,
    EM_IP_INFO_APPDIREC,
    EM_IP_INFO_TCPFLAGSBYCLI,
    EM_IP_INFO_TCPFLAGSBYSRV,
    EM_IP_INFO_SESPAYLEN,
    EM_IP_INFO_UPLINKPKTNUM,
    EM_IP_INFO_UPLINESKBYT,
    EM_IP_INFO_UPLINKDATABYTES,
    EM_IP_INFO_UPLINKDESBYTES,
    EM_IP_INFO_DOWNLINKPKTNUM,
    EM_IP_INFO_DOWNBYTESLINK,
    EM_IP_INFO_DOWNLINKDATABYTES,
    EM_IP_INFO_DOWNLINKDESBYTES,
    EM_IP_INFO_NSEQNUMUPLINKSY,
    EM_IP_INFO_DOWNLINKSYNSEQNUM,
    EM_IP_INFO_UPLINKSYNTCPWINS,
    EM_IP_INFO_DOWNLINKSYNTCPWINS,
    EM_IP_INFO_UPLINKTCPOPTS,
    EM_IP_INFO_DOWNLINKTCPOPTS,
    EM_IP_INFO_UPLINKTRANSPAYHEX,
    EM_IP_INFO_DOWNLINKTRANSPAYHEX,
    EM_IP_INFO_UPLINKPAYLENSET,
    EM_IP_INFO_DOWNLINKPAYLENSET,
    EM_IP_INFO_UPLINKBIGPKTLEN,
    EM_IP_INFO_UPLINKSMAPKTLEN,
    EM_IP_INFO_UPLINKBIGPKTINT,
    EM_IP_INFO_UPLINKSMAPKTINT,
    EM_IP_INFO_DOWNLINKBIGPKTLEN,
    EM_IP_INFO_DOWNLINKSMAPKTLEN,
    EM_IP_INFO_DOWNLINKBIGPKTINT,
    EM_IP_INFO_DOWNLINKSMAPKTINT,
    EM_IP_INFO_TCPFLAGSSYNCNT,
    EM_IP_INFO_TCPFLAGSSYNACKCNT,
    EM_IP_INFO_TCPFLAGSACKCNT,
    EM_IP_INFO_TCPFLAGSPSHCNT,
    EM_IP_INFO_TCPFLAGSRSTCNT,
    EM_IP_INFO_TCPFLAGSURGCNT,
    EM_IP_INFO_CT,
    EM_IP_INFO_IPHEADCV,
    EM_IP_INFO_ETAGS,
    EM_IP_INFO_TTAGS,


    EM_IP_INFO_MAX
}dpi_ip_info_em;

enum dbbasic_index_em{
   EM_DBBASIC_DBTYPE,
   EM_DBBASIC_USERNAME,
   EM_DBBASIC_PASSWORD,
   EM_DBBASIC_DBNAME,
   EM_DBBASIC_DBSQL,
   EM_DBBASIC_DBIP,
   EM_DBBASIC_DBPORT,
   EM_DBBASIC_ORACLE_HOST,

   EM_DBBASIC_MAX
};

#define DPI_FIELD_D(index, type, field_name)  {(index), (type), (field_name)}

extern dpi_field_table dpi_common_field[];

typedef const char* repeat_str;

// ip地理位置
void dissect_ip2region(ip2region_t st, uint8_t *ip4, IPINFO* IP);
void get_ip_position_from_ip(const char* ip_address, IPINFO* pIp);

int get_ip_asn_info(MMDB_s *const mmdb,const char *ip_address,char *buff, int len);
int get_ip_latitude_longitude_info(MMDB_s *const mmdb,const char *ip_address,const char *itude, char *buff, int len);


void dissect_ip_position(struct flow_info *flow, int direction, IPINFO *srcIp, IPINFO *dstIp);

char* getRandByMicrosecond(char* ouput, int size, int len);
void free_buf(char* p);


#define GEOIPASNPATH "./GeoLite2-ASN.mmdb"
//#define GEOIPDBPATH "../lib/GeoLite2-City.mmdb"    //GeoLite2数据库文件路径
#define GEOIPDBPATH "./GeoLite2-City.mmdb"    //GeoLite2数据库文件路径

#define TMPFILEPATH "./ip_"          //临时文件路径头部
#define FILETYPE    ".txt"           //临时文件尾部,文件格式或类型
#define FILETXTLEN     50            //临时文件名最大长度
#define RANDLEN        10            //临时文件名中随机数的长度

/************************************   test for field write **************************************/

// json tbl
#define JSON_FIELD_MAX  512         // 最大字段数量
#define JSON_FIELD_MAX_LEN  32      // 最大字段长度
#define JSON_PROTO_MAX_LEN  32      // 协议名最大长度

typedef struct json_field_t_
{
    char        proto_name[32];
    uint16_t    proto_id;           // 内部自定义协议号
    uint16_t    field_cnt;          // 字段数量(不包括公共字段)
    char        field[JSON_FIELD_MAX][JSON_FIELD_MAX_LEN];    // 字段列表
}JsonField;

struct tbl_log_file_node
{
    FILE *fp_tbl;
    unsigned int  log_num;
    unsigned int  timeout_sec;
    char filename[128];     // 文件名, 不含 格式后缀 和 .tmp后缀
};


// create tbl from mempool
struct tbl_log * dpi_tbl_create(struct tbl_log **tbl);
// 内存相关接口
/**
*  克隆tbl_log对象(浅拷贝，引用计数+1)
*/
struct tbl_log * dpi_tbl_clone_ref(struct tbl_log *tbl);
void dpi_tbl_free(struct tbl_log *tbl);

uint8_t dpi_app_match_res_enqueue(void *tbl);

// end of json tbl

void tbl_log_file_close_writing(void);

void  register_plugin_tbl_array(enum tbl_log_type type, int content, const char *name,call_dissector_init_func func );

void register_tbl_array(enum tbl_log_type type, int content, const char *name,call_dissector_init_func func );

int get_macstring(char *__str, int len, const uint8_t *mac);

int get_ip4string(char *__str, int len, uint32_t ip);
int get_ip6string(char *__str, int len, const uint8_t *ip);
int get_iparray_to_string(char *__str, int len,const uint8_t* ip);
void get_ipstring(uint8_t version, char *input, char *output, uint16_t o_len);
uint32_t dotted_to_addr(const char* ip_string);

int get_now_datetime(char *time_str, int len);

void Nextval(char T[],int *next);

int KMP(char S[] ,int S_len,char T[], int lenT);

int detect_file_type(const char *payload,uint16_t payload_len, char *buff);

int write_n_empty_reconds       (precord_t *record, int *idx, int max_len, int n);
int write_one_str_reconds       (precord_t *record, int *idx, int max_len, const char *data, unsigned int data_len);
int write_one_chars_reconds     (precord_t *record, int *idx, int max_len, const char *data, unsigned int data_len);
int write_string_reconds        (precord_t *record, int *idx, int max_len, const char *data);
int write_fstring_reconds       (precord_t *record, int *idx, int max_len, const char *format, ...);
int write_one_ip_reconds        (precord_t *record, int *idx, uint8_t version, const uint8_t *str);
int write_one_num_reconds       (precord_t *record, int *idx, int max_len, uint32_t data);
int write_one_no_zero_num_reconds(precord_t *record, int *idx, int max_len, uint32_t data);
int write_one_snum_reconds      (precord_t *record, int *idx, int max_len, long data);
int write_one_hexnum_reconds    (precord_t *record, int *idx, int max_len, uint32_t data);
int write_one_u64_hexnum_reconds(precord_t *record, int *idx, int max_len, uint64_t data);
int write_one_u48_hexnum_reconds(precord_t *record, int *idx, int max_len, uint64_t data);
int write_uint64_reconds        (precord_t *record, int *idx, int max_len, uint64_t data);
int write_multi_num_reconds     (precord_t *record, int *idx, int max_len, const uint8_t *data,uint32_t data_len);
int write_multi_bsdnum_reconds  (precord_t *record, int *idx, int max_len, const uint8_t *data,uint32_t data_len);
int write_multi_digit_reconds   (precord_t *record, int *idx, int max_len, const uint8_t *data,uint32_t data_len);
int write_one_hex_reconds       (precord_t *record, int *idx, int max_len, long long unsigned data);
int write_coupler_log           (precord_t *record, int *idx, int max_len, uint8_t data_type,const uint8_t *data,uint64_t int_data);
int write_hex_to_string(char *bufer, int buff_len, const uint8_t *data,uint32_t data_len);
int write_fields_data_array(FieldValue_t *field_array, FieldType_t field_type, const uint8_t *field_ptr,uint64_t data_len);

int std_write_ip_array(FieldValue_t *fp, FieldType_t type, uint8_t version, const uint8_t * str);


void write_tbl_log_common(struct flow_info *flow, int direction, struct tbl_log *log_ptr, int *idx, int log_len_max, SdtMatchResult *match_result);

precord_t* write_sdt_syslog(SdtMatchResult *match_result);
int write_sdt_rule_info(SdtMatchResult *match_result, precord_t *record);

int write_327ZDY_task_info(SdtMatchResult *match_result, precord_t *record, const char* pcap_file_path);

int record_2_string(precord_t *record, char *buff, int size);
int record_2_file(precord_t *record, FILE *fp);

int write_proto_field_tab(dpi_field_table *proto_array,int max_len,const char *proto_name);

int write_proto_field_tab_ex(dpi_field_table *proto_arr, int max_len, const char *proto_name, repeat_str* str_array, int str_size, int group_num);
int plugin_write_proto_field_tab(char **proto_array,int max_len,const char *proto_name);

enum proto_id_mode{
  EM_TBL_LOG_ID_BY_DEFAULT,
};
void init_log_ptr_data(struct tbl_log *log_ptr, struct flow_info *flow,int tbl_log_proto_id);

int write_tbl_log(struct tbl_log *log);
uint8_t equeue_app_fields_by_copy(void *tlog);

void *write_tbl_log_to_file_func(void * arg);

int init_tbl_log(void);

int get_filename(char *path_name, char *name);


int init_protocol_reflect_fields(void   *field_hash,
                                char **reflect_protoname,
                                int **reflect_array,
                                int *reflect_array_num,
                                const char *proto_name,
                                enum PROTOCOL_TYPE protocol_id);


int map_fields_info_register(dpi_field_table *protocol_array,
                              enum PROTOCOL_TYPE protocol_id,
                              int max_fields_num,
                              const char *protocol_name);



int map_fields_get_raw_num(enum PROTOCOL_TYPE protocol_id);

int map_fields_get_num(enum PROTOCOL_TYPE protocol_id);
int *map_fields_get_array(enum PROTOCOL_TYPE protocol_id);
void *map_fields_get_hash_tab(enum PROTOCOL_TYPE protocol_id);


int write_tbl_log_share_header(struct tbl_log *log_ptr, int log_len_max, struct flow_info *flow, int direction);
int write_shared_header(precord_t *record, int log_len_max, struct flow_info *flow, int direction);

/* 获取IP对应的ASN自治号 */
void get_asn_from_ip(const char *ip, char *asn, int len);

int create_tbl_log_file_hash_table(void);

#define SCHEMA_COMMON_FIELD_TRAILER     "schema_trailer"

/*======================================================================================================
 * 注册 protocol schema
 *=====================================================================================================*/
int dpi_register_proto_schema(dpi_field_table *proto_array,int max_len,const char *proto_name);

int dpi_register_proto_schema_ex(dpi_field_table *proto_array, int max_len, const char *proto_name, char *common_field_schema);

int dpi_register_proto_schema_enginew(void *ws_proto_tbl,int max_len,const char *proto_name);

int plugin_write_proto_field_tab(char **proto_array,int max_len,const char *proto_name);

int dpi_register_proto(const char * proto_name, const char * proto_full_name,char * common_field_schema);

/*======================================================================================================
 * 适配 protocol schema
 *=====================================================================================================*/
int dpi_adapt_proto_schema();

/* pschema */
int dpi_pschema_get_common_field(dpi_field_table *field_table_array[]);
int dpi_pschema_get_common_field_trailer(dpi_field_table *field_table_array[]);

precord_t *sdt_precord_clone(precord_t *record);
void sdt_precord_destroy(precord_t *record);

#define dpi_precord_new_record(record, proto_name, proto_field_table)   \
{                                                                       \
    record = sdt_precord_new_record(proto_name);                       \
}

const char *dpi_precord_get_proto_name(precord_t *record);


int dpi_pschema_get_link_field(dpi_field_table *field_table_array[]);

void tbl_out_stop(void);

void record_show(precord_t *record);

void del_not_match_tbl_file(precord_t *record, uint16_t proto_id);

#endif
